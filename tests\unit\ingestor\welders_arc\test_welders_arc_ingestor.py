import unittest
from datetime import datetime
from uuid import uuid4

from omnicat.ingestor.welders_arc.models import Elset_Sgp4Xp, MessageHeader
from omnicat.ingestor.welders_arc.welders_arc_ingestor import get_tracks, _add_checksum_if_needed
from omnicat.models import ObjectCorrelation
from omnicat.models.base_models import TLE


class TestWeldersArcIngestor(unittest.TestCase):
    def test_get_tracks(self):
        # Create sample data
        elset_sgp4xp = Elset_Sgp4Xp(
            idElset="0af401eb-e6ec-4af5-95d5-6b59bcb1d172",
            classificationMarking="U",
            satNo="28814",
            epoch="2025-04-06T07:56:47.109Z",
            # 68 chars, checksum will be auto-added
            line1="1 28814U          25096.33110089 +.00000000  88294-1  43288 0 4 0001",
            # 68 chars, checksum will be auto-added
            line2="2 28814  26.3177 173.3992 6772806  53.7416   0.8687  2.5997730900001",
            createdAt="2025-04-08T01:44:59.681",
            createdBy="producer.pulsar",
            origin="COMSPOC",
            source="Space Book",
            ephemType=4,
            uct=False,
            origObjectId="28814",
            dataMode="REAL",
            algorithm="SGP4-XP",
            origNetwork="SDA TAP"
        )

        # Create sample message header
        header = MessageHeader(
            messageId=str(uuid4()),
            messageTime=datetime.now(),
            messageVersion="1.0",
            subsystem="ss2",
            dataProvider="welders_arc",
            dataType="ss2.data.elset.sgp4-xp",
            dataVersion="1.0"
        )

        # Test without object correlation
        topic = "ss2.data.elset.sgp4-xp"
        tracks = get_tracks([elset_sgp4xp], [header], topic)

        # Verify results
        self.assertEqual(len(tracks), 1)
        track = tracks[0]

        # Check track properties
        self.assertEqual(track.source, topic)
        self.assertEqual(track.format, "tle")
        self.assertIsInstance(track.data, TLE)

        # Check that line1 equals the original line plus its checksum
        checksum1 = TLE._calculate_checksum(elset_sgp4xp.line1)
        self.assertEqual(track.data.line1, f"{elset_sgp4xp.line1}{checksum1}")

        # Check that line2 equals the original line plus its checksum
        checksum2 = TLE._calculate_checksum(elset_sgp4xp.line2)
        self.assertEqual(track.data.line2, f"{elset_sgp4xp.line2}{checksum2}")

        # Check metadata
        self.assertIn("welders_arc_elset", track.metadata)
        self.assertIn("welders_arc_message_headers", track.metadata)

        object_ids = ["object-123"]

        tracks_with_corr = get_tracks(
            [elset_sgp4xp], [header], topic,
            object_ids=object_ids
        )

        # Verify results with object correlation
        self.assertEqual(len(tracks_with_corr), 1)
        track_with_corr = tracks_with_corr[0]

        # Check lines with checksums in the object correlation case too
        self.assertEqual(track_with_corr.data.line1,
                         f"{elset_sgp4xp.line1}{checksum1}")
        self.assertEqual(track_with_corr.data.line2,
                         f"{elset_sgp4xp.line2}{checksum2}")

        # Check object correlations
        self.assertIsNotNone(track_with_corr.object_correlations)
        self.assertEqual(len(track_with_corr.object_correlations), 1)
        self.assertIsInstance(
            track_with_corr.object_correlations[0], ObjectCorrelation)
        self.assertEqual(
            track_with_corr.object_correlations[0].object_id, "object-123")
        self.assertEqual(
            track_with_corr.object_correlations[0].confidence, 1.0)

    def test_add_checksum_if_needed(self):
        """Test the _add_checksum_if_needed helper function."""
        # Test with 68-character line (should add checksum)
        line_68 = "1 28814U          25096.33110089 +.00000000  88294-1  43288 0 4 0001"
        result_68 = _add_checksum_if_needed(line_68)
        self.assertEqual(len(result_68), 69)
        self.assertEqual(result_68, line_68 + "4")  # Expected checksum is 4

        # Test with 69-character line (should return unchanged)
        line_69 = "1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992"
        result_69 = _add_checksum_if_needed(line_69)
        self.assertEqual(len(result_69), 69)
        self.assertEqual(result_69, line_69)  # Should be unchanged

        # Test with different length line (should return unchanged)
        line_67 = "1 28814U          25096.33110089 +.00000000  88294-1  43288 0 4 000"
        result_67 = _add_checksum_if_needed(line_67)
        self.assertEqual(len(result_67), 67)
        self.assertEqual(result_67, line_67)  # Should be unchanged

    def test_elset_getIdOnOrbit_fallback_strategies(self):
        """Test that getIdOnOrbit() correctly uses fallback strategies for both elset types."""
        from omnicat.ingestor.welders_arc.models.elset import Elset_UctCandidate, Elset_Sgp4Xp

        # Test Elset_UctCandidate fallback strategies

        # Test data missing satNo and idOnOrbit (should extract from TLE)
        uct_data = {
            'idElset': '6d104420-90cf-407d-a890-3c78a418cb66',
            'classificationMarking': 'U//PR-LSAS-SV',
            'epoch': '2025-05-12T17:24:20.3382624Z',
            'meanMotion': 1.0162375,
            'source': 'LSAS',
            'eccentricity': 0.0097096,
            'inclination': 0.1072,
            'raan': 287.765,
            'argOfPerigee': 103.4929,
            'meanAnomaly': 119.126,
            'revNo': 1,
            'bStar': -1828600,
            'meanMotionDot': -0.00023087,
            'meanMotionDDot': 0,
            'semiMajorAxis': 41789.9327545782,
            'period': 1416.991598912656,
            'apogee': 42195.696285652055,
            'perigee': 41384.169223504345,
            'line1': '1 99999U          25132.72523591 -.00023087  00000-0 -18286**** 00019',
            'line2': '2 99999 000.1072 287.7650 0097096 103.4929 119.1260 01.01623750000017',
            'dataMode': 'REAL',
            'algorithm': 'ODTK',
            'sourcedData': ['03f39f2e-318e-431e-8e3f-33c57ca77df7'],
            'sourcedDataTypes': ['EO']
        }

        # Create elset (missing satNo and idOnOrbit)
        uct_elset = Elset_UctCandidate.model_validate(uct_data)

        # Verify original state
        self.assertIsNone(uct_elset.satNo)
        self.assertIsNone(uct_elset.idOnOrbit)

        # Test that getIdOnOrbit() extracts from TLE line1
        # Should extract from TLE
        self.assertEqual(uct_elset.getIdOnOrbit(), "99999")

        # Test with satNo present but idOnOrbit missing
        uct_data_with_satno = uct_data.copy()
        uct_data_with_satno['satNo'] = 12345
        uct_elset_with_satno = Elset_UctCandidate.model_validate(
            uct_data_with_satno)

        self.assertEqual(uct_elset_with_satno.getIdOnOrbit(),
                         "12345")  # Should use satNo

        # Test with both idOnOrbit and satNo present (should prefer idOnOrbit)
        uct_data_complete = uct_data.copy()
        uct_data_complete['satNo'] = 12345
        uct_data_complete['idOnOrbit'] = "67890"
        uct_elset_complete = Elset_UctCandidate.model_validate(
            uct_data_complete)

        self.assertEqual(uct_elset_complete.getIdOnOrbit(),
                         "67890")  # Should prefer idOnOrbit

        # Test Elset_Sgp4Xp fallback strategies
        sgp4xp_data = {
            'idElset': '6d104420-90cf-407d-a890-3c78a418cb66',
            'classificationMarking': 'U//PR-LSAS-SV',
            'satNo': 99999,
            'epoch': '2025-05-12T17:24:20.3382624Z',
            'line1': '1 99999U          25132.72523591 -.00023087  00000-0 -18286**** 00019',
            'line2': '2 99999 000.1072 287.7650 0097096 103.4929 119.1260 01.01623750000017',
            'createdAt': '2025-05-12T17:24:20.3382624Z',
            'createdBy': 'system.test',
            'origin': 'LSAS',
            'source': 'LSAS',
            'ephemType': 1,
            'uct': False,
            'origObjectId': None,  # Missing origObjectId
            'dataMode': 'REAL',
            'algorithm': 'SGP4',
            'origNetwork': 'TAPNET'
        }

        sgp4xp_elset = Elset_Sgp4Xp.model_validate(sgp4xp_data)

        # Should fall back to satNo when origObjectId is None
        self.assertEqual(sgp4xp_elset.getIdOnOrbit(), "99999")

        # Test with origObjectId present (should prefer it)
        sgp4xp_data_with_orig = sgp4xp_data.copy()
        sgp4xp_data_with_orig['origObjectId'] = "ORIG-12345"
        sgp4xp_elset_with_orig = Elset_Sgp4Xp.model_validate(
            sgp4xp_data_with_orig)

        self.assertEqual(sgp4xp_elset_with_orig.getIdOnOrbit(), "ORIG-12345")

    def test_elset_getIdOnOrbit_validates_satno_range(self):
        """Test that getIdOnOrbit() validates satellite catalog number ranges."""
        from omnicat.ingestor.welders_arc.models.elset import Elset_UctCandidate

        # Test data with invalid satNo values
        base_data = {
            'idElset': '6d104420-90cf-407d-a890-3c78a418cb66',
            'classificationMarking': 'U//PR-LSAS-SV',
            'epoch': '2025-05-12T17:24:20.3382624Z',
            'meanMotion': 1.0162375,
            'source': 'LSAS',
            'eccentricity': 0.0097096,
            'inclination': 0.1072,
            'raan': 287.765,
            'argOfPerigee': 103.4929,
            'meanAnomaly': 119.126,
            'revNo': 1,
            'bStar': -1828600,
            'meanMotionDot': -0.00023087,
            'meanMotionDDot': 0,
            'semiMajorAxis': 41789.9327545782,
            'period': 1416.991598912656,
            'apogee': 42195.696285652055,
            'perigee': 41384.169223504345,
            'line1': '1 00000U          25132.72523591 -.00023087  00000-0 -18286**** 00019',  # Invalid: 0
            'line2': '2 00000 000.1072 287.7650 0097096 103.4929 119.1260 01.01623750000017',
            'dataMode': 'REAL',
            'algorithm': 'ODTK',
            'sourcedData': ['03f39f2e-318e-431e-8e3f-33c57ca77df7'],
            'sourcedDataTypes': ['EO']
        }

        # Test with satNo = 0 (invalid)
        data_with_zero = base_data.copy()
        data_with_zero['satNo'] = 0
        elset_zero = Elset_UctCandidate.model_validate(data_with_zero)
        # Should reject invalid satNo and TLE
        self.assertIsNone(elset_zero.getIdOnOrbit())

        # Test with satNo > 99999 (invalid)
        data_with_large = base_data.copy()
        data_with_large['satNo'] = 100000
        # Valid TLE
        data_with_large['line1'] = '1 99999U          25132.72523591 -.00023087  00000-0 -18286**** 00019'
        elset_large = Elset_UctCandidate.model_validate(data_with_large)
        # Should fall back to valid TLE
        self.assertEqual(elset_large.getIdOnOrbit(), "99999")

        # Test with negative satNo (invalid)
        data_with_negative = base_data.copy()
        data_with_negative['satNo'] = -1
        # Valid TLE
        data_with_negative['line1'] = '1 99999U          25132.72523591 -.00023087  00000-0 -18286**** 00019'
        elset_negative = Elset_UctCandidate.model_validate(data_with_negative)
        # Should fall back to valid TLE
        self.assertEqual(elset_negative.getIdOnOrbit(), "99999")

        # Test with valid satNo at boundaries
        data_with_one = base_data.copy()
        data_with_one['satNo'] = 1
        elset_one = Elset_UctCandidate.model_validate(data_with_one)
        # Should accept satNo = 1
        self.assertEqual(elset_one.getIdOnOrbit(), "1")

        data_with_max = base_data.copy()
        data_with_max['satNo'] = 99999
        elset_max = Elset_UctCandidate.model_validate(data_with_max)
        # Should accept satNo = 99999
        self.assertEqual(elset_max.getIdOnOrbit(), "99999")

    def test_helper_functions(self):
        """Test the helper functions _is_valid_satno and _extract_satno_from_tle."""
        from omnicat.ingestor.welders_arc.models.elset import _is_valid_satno, _extract_satno_from_tle

        # Test _is_valid_satno function

        # Valid values
        self.assertTrue(_is_valid_satno(1))
        self.assertTrue(_is_valid_satno("1"))
        self.assertTrue(_is_valid_satno(25544))  # ISS
        self.assertTrue(_is_valid_satno("25544"))
        self.assertTrue(_is_valid_satno(99999))
        self.assertTrue(_is_valid_satno("99999"))

        # Invalid values
        self.assertFalse(_is_valid_satno(0))
        self.assertFalse(_is_valid_satno("0"))
        self.assertFalse(_is_valid_satno(-1))
        self.assertFalse(_is_valid_satno("-1"))
        self.assertFalse(_is_valid_satno(100000))
        self.assertFalse(_is_valid_satno("100000"))
        self.assertFalse(_is_valid_satno(None))
        self.assertFalse(_is_valid_satno(""))
        self.assertFalse(_is_valid_satno("abc"))
        self.assertFalse(_is_valid_satno([]))
        self.assertFalse(_is_valid_satno({}))

        # Test _extract_satno_from_tle function

        # Valid TLE lines
        self.assertEqual(_extract_satno_from_tle(
            "1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992"), "25544")
        self.assertEqual(_extract_satno_from_tle(
            "1 99999U          25132.72523591 -.00023087  00000-0 -18286**** 00019"), "99999")
        self.assertEqual(_extract_satno_from_tle(
            "1 00001U          25132.72523591 -.00023087  00000-0 -18286**** 00019"), "00001")

        # Invalid TLE lines
        self.assertIsNone(_extract_satno_from_tle(
            "1 00000U          25132.72523591 -.00023087  00000-0 -18286**** 00019"))  # 0 is invalid
        self.assertIsNone(_extract_satno_from_tle(
            "1 ABCDEU          25132.72523591 -.00023087  00000-0 -18286**** 00019"))  # Non-numeric
        self.assertIsNone(_extract_satno_from_tle(""))  # Empty string
        self.assertIsNone(_extract_satno_from_tle(None))  # None
        self.assertIsNone(_extract_satno_from_tle("1 "))  # Too short
        self.assertIsNone(_extract_satno_from_tle(
            "invalid line"))  # Invalid format


if __name__ == '__main__':
    unittest.main()
