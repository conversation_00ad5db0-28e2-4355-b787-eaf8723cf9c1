from typing import TypeAlias
import omnicat.ingestor.udl.models as udl_models
from omnicat.ingestor.udl.models.elset import IdElset, ClassificationMarking, Line, Origin, Source, OrigObjectId, Algorithm, OrigNetwork, DataMode, SourcedDataType
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID

Elset_Full: TypeAlias = udl_models.Elset_Full

# TODO: satno should be a string
# TODO: field descriptions!


def _is_valid_satno(satno_value) -> bool:
    """
    Check if a satellite catalog number is valid.

    Args:
        satno_value: The satellite catalog number to validate (can be int, str, or None)

    Returns:
        True if the satellite catalog number is in the valid range (1-99999), False otherwise
    """
    if satno_value is None:
        return False

    try:
        sat_no = int(satno_value)
        return 1 <= sat_no <= 99999
    except (ValueError, TypeError):
        return False


def _extract_satno_from_tle(line1: str) -> str | None:
    """
    Extract satellite catalog number from TLE line1 if valid.

    Args:
        line1: The first line of a TLE (Two-Line Element)

    Returns:
        The satellite catalog number as a string if valid, None otherwise
    """
    if not line1:
        return None

    try:
        # Extract catalog number from TLE line1 (positions 2-7)
        catalog_number = line1[2:7].strip()
        if catalog_number and catalog_number.isdigit():
            if _is_valid_satno(catalog_number):
                return catalog_number
    except (IndexError, ValueError):
        pass

    return None


class Elset_Sgp4(BaseModel):
    idElset: IdElset
    satNo: int | str
    epoch: datetime
    dataMode: DataMode
    source: Source
    classificationMarking: ClassificationMarking

    def getIdOnOrbit(self) -> str | None:
        if self.satNo is not None:
            return str(self.satNo)
        return None


class Elset_UctCandidate(BaseModel):
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | str | None = None
    epoch: datetime
    meanMotion: float
    idOnOrbit: str | None = None
    eccentricity: float
    inclination: float
    raan: float
    argOfPerigee: float
    meanAnomaly: float
    revNo: int
    bStar: float
    meanMotionDot: float
    meanMotionDDot: float
    semiMajorAxis: float
    period: float
    apogee: float
    perigee: float
    line1: Line
    line2: Line
    sourcedData: list[str]
    sourcedDataTypes: list[SourcedDataType]
    createdBy: str | None = None
    source: Source
    dataMode: DataMode
    algorithm: Algorithm
    origNetwork: OrigNetwork | None = None

    def getIdOnOrbit(self) -> str | None:
        """
        Get the on-orbit identifier using multiple fallback strategies:
        1. Use idOnOrbit if available
        2. Use satNo if available and valid (1-99999)
        3. Extract satNo from TLE line1 if available and valid (1-99999)

        Returns None if no valid identifier can be found.
        """
        # Strategy 1: Use idOnOrbit if available
        if self.idOnOrbit is not None:
            return self.idOnOrbit

        # Strategy 2: Use satNo if available and valid
        if _is_valid_satno(self.satNo):
            return str(self.satNo)

        # Strategy 3: Extract satNo from TLE line1 if available and valid
        if hasattr(self, 'line1'):
            tle_satno = _extract_satno_from_tle(self.line1)
            if tle_satno is not None:
                return tle_satno

        return None


class Elset_Sgp4Xp(BaseModel):
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | str
    epoch: str
    line1: Line
    line2: Line
    createdAt: str
    createdBy: str
    origin: Origin
    source: Source
    ephemType: int
    uct: bool
    origObjectId: OrigObjectId
    dataMode: str
    algorithm: Algorithm
    origNetwork: OrigNetwork

    def getIdOnOrbit(self) -> str | None:
        """
        Get the on-orbit identifier using multiple fallback strategies:
        1. Use origObjectId if available
        2. Use satNo if available and valid (1-99999)
        3. Extract satNo from TLE line1 if available and valid (1-99999)

        Returns None if no valid identifier can be found.
        """
        # Strategy 1: Use origObjectId if available
        if self.origObjectId is not None:
            return str(self.origObjectId)

        # Strategy 2: Use satNo if available and valid
        if _is_valid_satno(self.satNo):
            return str(self.satNo)

        # Strategy 3: Extract satNo from TLE line1 if available and valid
        if hasattr(self, 'line1'):
            tle_satno = _extract_satno_from_tle(self.line1)
            if tle_satno is not None:
                return tle_satno

        return None
